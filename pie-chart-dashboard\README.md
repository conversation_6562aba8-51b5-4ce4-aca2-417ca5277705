# 飞书多维表格仪表盘饼状图插件

这是一个为飞书多维表格仪表盘开发的饼状图插件，可以帮助用户在仪表盘上直观展示数据分布情况。

## 功能特性

1. **数据可视化**：将多维表格数据转换为直观的饼状图
2. **多状态适配**：适配仪表盘的创建、配置、展示和全屏四种状态
3. **灵活配置**：支持表格选择、字段选择、样式配置等
4. **国际化支持**：支持中文、英文和日文三种语言
5. **主题适配**：适配飞书仪表盘的主题样式，包括全屏深色模式
6. **响应式设计**：适配不同屏幕尺寸和容器大小

## 技术架构

- **前端框架**：React 18 + TypeScript
- **构建工具**：Vite
- **图表库**：ECharts (echarts-for-react)
- **UI 组件库**：Semi Design（使用飞书仪表盘定制主题）
- **SDK**：@lark-base-open/js-sdk
- **国际化**：i18next + react-i18next

## 目录结构

```
pie-chart-dashboard/
├── src/
│   ├── components/
│   │   ├── PieChart/          # 主要的饼状图组件
│   │   │   ├── index.tsx      # 组件主文件
│   │   │   ├── style.scss     # 样式文件
│   │   │   └── utils.ts       # 工具函数
│   │   └── Item/              # 通用配置项组件
│   ├── hooks/
│   │   └── index.ts           # 自定义 Hooks
│   ├── locales/               # 国际化文件
│   │   ├── zh.json            # 中文
│   │   ├── en.json            # 英文
│   │   ├── jp.json            # 日文
│   │   └── i18n.ts            # 国际化配置
│   ├── App.tsx                # 应用入口
│   ├── App.scss               # 全局样式
│   └── index.tsx              # 渲染入口
├── package.json
├── vite.config.js
└── tsconfig.json
```

## 开发指南

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览构建结果

```bash
npm run preview
```

## 配置说明

插件支持以下配置选项：

- **数据源配置**
  - 表格选择：选择要展示数据的表格
  - 分组字段：用于饼图分组的字段
  - 数值字段：用于计算饼图数值的字段

- **显示配置**
  - 图表标题：自定义图表标题
  - 显示标题：是否显示图表标题
  - 显示图例：是否显示图例
  - 显示标签：是否显示数据标签

- **样式配置**
  - 配色方案：选择不同的颜色主题
  - 图表大小：小、中、大三种尺寸

## 使用方法

1. 在飞书多维表格中打开仪表盘
2. 点击"添加组件"，选择"更多"进入插件市场
3. 点击"添加自定义插件"，输入插件地址
4. 配置数据源和显示选项
5. 保存配置即可在仪表盘中查看饼状图

## 注意事项

- 确保选择的数值字段包含数字类型数据
- 分组字段建议选择文本或选择类型字段
- 插件会自动聚合相同分组的数据
- 支持实时数据更新和协同编辑

## 许可证

ISC License
