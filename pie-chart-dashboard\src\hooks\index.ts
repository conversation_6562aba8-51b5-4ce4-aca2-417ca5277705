import { DashboardState, bitable, dashboard, base, SourceType, Rollup } from "@lark-base-open/js-sdk";
import React from "react";
import { useLayoutEffect, useState, useEffect } from "react";

function updateTheme(theme: string) {
  document.body.setAttribute('theme-mode', theme);
}

/** 跟随主题色变化 */
export function useTheme() {
  const [bgColor, setBgColor] = useState('#ffffff');
  useLayoutEffect(() => {
    dashboard.getTheme().then((res) => {
      setBgColor(res.chartBgColor);
      updateTheme(res.theme.toLocaleLowerCase());
    })

    dashboard.onThemeChange((res) => {
      setBgColor(res.data.chartBgColor);
      updateTheme(res.data.theme.toLocaleLowerCase());
    })
  }, [])
  return {
    bgColor,
  }
}

/** 初始化、更新config */
export function useConfig(updateConfig: (data: any) => void) {
  const isCreate = dashboard.state === DashboardState.Create
  React.useEffect(() => {
    if (isCreate) {
      return
    }
    // 初始化获取配置
    dashboard.getConfig().then(updateConfig);
  }, []);

  React.useEffect(() => {
    const offConfigChange = dashboard.onConfigChange((r) => {
      // 监听配置变化，协同修改配置
      updateConfig(r.data);
    });
    return () => {
      offConfigChange();
    }
  }, []);
}

// 获取表格列表的Hook
export function useTableList() {
  const [tables, setTables] = useState<Array<{label: string, value: string}>>([]);

  useEffect(() => {
    const fetchTables = async () => {
      try {
        const tableList = await base.getTableList();
        const formattedTables = await Promise.all(
          tableList.map(async (table) => {
            const name = await table.getName();
            return {
              label: name,
              value: JSON.stringify({ id: table.id, name })
            };
          })
        );
        setTables(formattedTables);
      } catch (error) {
        console.error('获取表格列表失败:', error);
      }
    };

    fetchTables();
  }, []);

  return tables;
}

// 获取字段列表的Hook
export function useFieldList(tableId: string) {
  const [fields, setFields] = useState<Array<{label: string, value: string}>>([]);

  useEffect(() => {
    if (!tableId) {
      setFields([]);
      return;
    }

    const fetchFields = async () => {
      try {
        const table = await base.getTableById(tableId);
        const fieldMetaList = await table.getFieldMetaList();
        
        const formattedFields = fieldMetaList.map((field) => ({
          label: field.name,
          value: JSON.stringify({ id: field.id, name: field.name })
        }));
        
        setFields(formattedFields);
      } catch (error) {
        console.error('获取字段列表失败:', error);
      }
    };

    fetchFields();
  }, [tableId]);

  return fields;
}
