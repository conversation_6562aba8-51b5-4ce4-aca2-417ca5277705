import './style.scss';
import React, { useLayoutEffect, useMemo, useState, useEffect } from 'react';
import { dashboard, bitable, DashboardState, IConfig, SourceType, Rollup } from "@lark-base-open/js-sdk";
import { Button, Select, Input, Switch, Radio, RadioGroup } from '@douyinfe/semi-ui';
import { useConfig, useTableList, useFieldList } from '../../hooks';
import classnames from 'classnames';
import { useTranslation } from 'react-i18next';
import { Item } from '../Item';
import ReactECharts from 'echarts-for-react';
import { getChartOption, processChartData, getColorSchemes } from './utils';

interface IPieChartConfig {
  tableId: string;
  tableName: string;
  groupFieldId: string;
  groupFieldName: string;
  valueFieldId: string;
  valueFieldName: string;
  title: string;
  showTitle: boolean;
  showLegend: boolean;
  showLabel: boolean;
  colorScheme: string;
  chartSize: string;
}

const defaultConfig: IPieChartConfig = {
  tableId: '',
  tableName: '',
  groupFieldId: '',
  groupFieldName: '',
  valueFieldId: '',
  valueFieldName: '',
  title: '',
  showTitle: true,
  showLegend: true,
  showLabel: true,
  colorScheme: 'default',
  chartSize: 'medium'
};

/** 饼状图组件 */
export default function PieChart(props: { bgColor: string }) {
  const { t, i18n } = useTranslation();
  const [config, setConfig] = useState<IPieChartConfig>(defaultConfig);
  const [chartData, setChartData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const isCreate = dashboard.state === DashboardState.Create;
  const isConfig = dashboard.state === DashboardState.Config || isCreate;

  // 获取表格和字段列表
  const tables = useTableList();
  const fields = useFieldList(config.tableId);

  useEffect(() => {
    if (isCreate) {
      setConfig({
        ...defaultConfig,
        title: t('title')
      });
    }
  }, [i18n.language, isCreate]);

  /** 更新配置 */
  const updateConfig = (res: IConfig) => {
    const { customConfig } = res;
    if (customConfig) {
      setConfig(customConfig as IPieChartConfig);
      // 自动化发送截图
      setTimeout(() => {
        dashboard.setRendered();
      }, 3000);
    }
  };

  useConfig(updateConfig);

  // 获取数据
  useEffect(() => {
    if (!config.tableId || !config.groupFieldId || !config.valueFieldId) {
      setChartData([]);
      return;
    }

    const fetchData = async () => {
      setLoading(true);
      try {
        let data;
        if (dashboard.state === DashboardState.View) {
          data = await dashboard.getData();
        } else {
          // 配置态下使用预览数据
          data = await dashboard.getPreviewData({
            tableId: config.tableId,
            dataRange: { type: SourceType.ALL },
            groups: [
              { fieldId: config.groupFieldId }
            ],
            series: [
              { fieldId: config.valueFieldId, rollup: Rollup.SUM }
            ]
          });
        }
        
        const processedData = processChartData(data, config);
        setChartData(processedData);
      } catch (error) {
        console.error('获取数据失败:', error);
        setChartData([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [config.tableId, config.groupFieldId, config.valueFieldId, dashboard.state]);

  // 生成图表配置
  const chartOption = useMemo(() => {
    return getChartOption(chartData, config, t);
  }, [chartData, config, t]);

  return (
    <main 
      style={{ backgroundColor: props.bgColor }} 
      className={classnames({ 'main-config': isConfig, 'main': true })}
    >
      <div className='content'>
        <PieChartView
          option={chartOption}
          loading={loading}
          config={config}
          isConfig={isConfig}
          t={t}
        />
      </div>
      {isConfig && (
        <ConfigPanel 
          config={config} 
          setConfig={setConfig} 
          tables={tables}
          fields={fields}
          t={t} 
        />
      )}
    </main>
  );
}

// 图表展示组件
interface IPieChartView {
  option: any;
  loading: boolean;
  config: IPieChartConfig;
  isConfig: boolean;
  t: any;
}

function PieChartView({ option, loading, config, isConfig, t }: IPieChartView) {
  if (!config.tableId || !config.groupFieldId || !config.valueFieldId) {
    return (
      <div className="empty-state">
        <div className="empty-text">{t('please.config')}</div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="loading-state">
        <div className="loading-text">加载中...</div>
      </div>
    );
  }

  return (
    <div className={classnames('chart-container', {
      'chart-container-config': isConfig,
      [`chart-size-${config.chartSize}`]: true
    })}>
      {config.showTitle && config.title && (
        <div className="chart-title">{config.title}</div>
      )}
      <ReactECharts
        option={option}
        style={{ width: '100%', height: '100%' }}
        opts={{ renderer: 'svg' }}
      />
    </div>
  );
}

// 配置面板组件
interface IConfigPanel {
  config: IPieChartConfig;
  setConfig: React.Dispatch<React.SetStateAction<IPieChartConfig>>;
  tables: Array<{label: string, value: string}>;
  fields: Array<{label: string, value: string}>;
  t: any;
}

function ConfigPanel({ config, setConfig, tables, fields, t }: IConfigPanel) {
  const colorSchemes = getColorSchemes(t);

  const handleSave = async () => {
    try {
      await dashboard.saveConfig({
        customConfig: config,
        dataConditions: [{
          tableId: config.tableId,
          dataRange: { type: SourceType.ALL },
          groups: [
            { fieldId: config.groupFieldId }
          ],
          series: [
            { fieldId: config.valueFieldId, rollup: Rollup.SUM }
          ]
        }],
      });
      await dashboard.setRendered();
    } catch (error) {
      console.error('保存配置失败:', error);
    }
  };

  return (
    <div className='config-panel'>
      <div className='form'>
        <Item label={t('label.table')}>
          <Select
            value={config.tableId ? JSON.stringify({ id: config.tableId, name: config.tableName }) : undefined}
            placeholder={t('placeholder.select.table')}
            style={{ width: '100%' }}
            onChange={(value) => {
              if (value) {
                const tableInfo = JSON.parse(value as string);
                setConfig({
                  ...config,
                  tableId: tableInfo.id,
                  tableName: tableInfo.name,
                  groupFieldId: '',
                  groupFieldName: '',
                  valueFieldId: '',
                  valueFieldName: ''
                });
              }
            }}
          >
            {tables.map((table) => (
              <Select.Option key={table.value} value={table.value}>
                {table.label}
              </Select.Option>
            ))}
          </Select>
        </Item>

        <Item label={t('label.group.field')}>
          <Select
            value={config.groupFieldId ? JSON.stringify({ id: config.groupFieldId, name: config.groupFieldName }) : undefined}
            placeholder={t('placeholder.select.field')}
            style={{ width: '100%' }}
            disabled={!config.tableId}
            onChange={(value) => {
              if (value) {
                const fieldInfo = JSON.parse(value as string);
                setConfig({
                  ...config,
                  groupFieldId: fieldInfo.id,
                  groupFieldName: fieldInfo.name
                });
              }
            }}
          >
            {fields.map((field) => (
              <Select.Option key={field.value} value={field.value}>
                {field.label}
              </Select.Option>
            ))}
          </Select>
        </Item>

        <Item label={t('label.value.field')}>
          <Select
            value={config.valueFieldId ? JSON.stringify({ id: config.valueFieldId, name: config.valueFieldName }) : undefined}
            placeholder={t('placeholder.select.field')}
            style={{ width: '100%' }}
            disabled={!config.tableId}
            onChange={(value) => {
              if (value) {
                const fieldInfo = JSON.parse(value as string);
                setConfig({
                  ...config,
                  valueFieldId: fieldInfo.id,
                  valueFieldName: fieldInfo.name
                });
              }
            }}
          >
            {fields.map((field) => (
              <Select.Option key={field.value} value={field.value}>
                {field.label}
              </Select.Option>
            ))}
          </Select>
        </Item>

        <Item label={t('label.chart.title')}>
          <Input
            value={config.title}
            placeholder={t('placeholder.enter.title')}
            onChange={(value) => setConfig({ ...config, title: value })}
          />
        </Item>

        <Item label={t('label.show.title')}>
          <Switch
            checked={config.showTitle}
            onChange={(checked) => setConfig({ ...config, showTitle: checked })}
          />
        </Item>

        <Item label={t('label.show.legend')}>
          <Switch
            checked={config.showLegend}
            onChange={(checked) => setConfig({ ...config, showLegend: checked })}
          />
        </Item>

        <Item label={t('label.show.label')}>
          <Switch
            checked={config.showLabel}
            onChange={(checked) => setConfig({ ...config, showLabel: checked })}
          />
        </Item>

        <Item label={t('label.color.scheme')}>
          <RadioGroup
            value={config.colorScheme}
            onChange={(e) => setConfig({ ...config, colorScheme: e.target.value })}
            direction="vertical"
          >
            {Object.entries(colorSchemes).map(([key, scheme]) => (
              <Radio key={key} value={key}>
                {scheme.name}
              </Radio>
            ))}
          </RadioGroup>
        </Item>

        <Item label={t('label.chart.size')}>
          <RadioGroup
            value={config.chartSize}
            onChange={(e) => setConfig({ ...config, chartSize: e.target.value })}
          >
            <Radio value="small">{t('size.small')}</Radio>
            <Radio value="medium">{t('size.medium')}</Radio>
            <Radio value="large">{t('size.large')}</Radio>
          </RadioGroup>
        </Item>
      </div>

      <Button
        className='btn'
        theme='solid'
        onClick={handleSave}
      >
        {t('confirm')}
      </Button>
    </div>
  );
}
