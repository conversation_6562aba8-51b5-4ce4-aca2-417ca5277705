.ant-btn-primary {
    background-color: rgba(20, 86, 240, 1);
}

.main {
    display: flex;
    box-sizing: border-box;
    min-height: 100vh;
    --number-gap: 4.2vmax;
}

.main-config {
    border-top: var(--divider) 0.5px solid;
    --number-gap: 2.1vmax;
}

.content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 0;
}

.config-panel {
    width: 340px;
    flex: none;
    display: flex;
    justify-content: flex-start;
    gap: 20px;
    flex-direction: column;
    border-left: var(--divider) 0.5px solid;
    padding: 20px;
    box-sizing: border-box;
}

.form {
    flex-grow: 1;
}

.ant-picker {
    width: 100%;
}

.checkbox-group {
    display: flex;
    row-gap: 10px;
    flex-wrap: wrap;
    .checkbox-group-item {
        width: 50%;
        flex-shrink: 0;
    }
}

.btn {
    align-self: flex-end;
    min-width: 80px;
}

.number-container {
    padding: 0 15px;
    display: flex;
    gap: var(--number-gap);
    justify-content: center;
    row-gap: 10px;
    flex-wrap: wrap;
    align-content: center;
}

.number-container-row {
    display: flex;
    gap: var(--number-gap);
}

.number-container div {
    flex-shrink: 0;
}

.number {
    font-family: DIN Alternate;
    font-size: 16vmax;
    font-weight: 700;
    line-height: 1em;
    text-align: center;
}

.number-config {
    font-size: 8vmax;
}

.number-title {
    font-family: PingFang SC;
    font-size: 3vmax;
    font-weight: 500;
    text-align: center;
    margin-top: 2vmax;
}

.number-title-config {
    font-size: 1.5vmax;
    margin-top: 1vmax;
}

.count-down-title {
    font-size: 3vmax;
    margin-bottom: 1vmax;
}

.count-down-title-config {
    font-size: 1.5vmax;
    font-weight: normal;
    margin-bottom: 0.5vmax;
}

.label-checkbox {
    display: flex;
    gap: 6px;
    align-items: center;
    justify-content: space-between;
}
