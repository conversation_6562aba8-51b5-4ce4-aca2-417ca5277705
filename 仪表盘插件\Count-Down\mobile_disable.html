<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body[theme-mode="light"] {
      color: black;
    }

    body[theme-mode="dark"] {
      color: white;
    }
  </style>
</head>

<body>
  <div
    style="width: 100%; height: 100%; display: flex; justify-content: center; align-items: center;flex-direction: column">
    <img style="height:0;width:auto;max-height:120px;max-width:120px;flex:1"
      src="data:image/svg+xml;base64,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"></img>
    <div id='content' style="margin-top: 3px">Content not available for this language</div>
  </div>
  <script type="module">
    import { bitable } from 'https://cdn.jsdelivr.net/npm/@lark-base-open/js-sdk@0.4.0/+esm'

    function updateTheme(theme) {
      document.body.setAttribute('theme-mode', theme);
    }
    const langMap = {
      'zh': '暂不支持展示该类内容',
      'zh-TW': '暫時不支援顯示此類型的內容',
      'zh-HK': '暫時不支援顯示此類型的內容',
      'en': 'Display of this type of content is not currently supported',
      'ja': 'この種類のコンテンツの表示は現在サポートされていません',
      'fr': "L'affichage de ce type de contenu n'est actuellement pas pris en charge",
      'hi': 'इस प्रकार की सामग्री का प्रदर्शन वर्तमान में समर्थित नहीं है',
      'id': 'Tampilan konten jenis ini saat ini tidak didukung',
      'it': 'La visualizzazione di questo tipo di contenuto attualmente non è supportata',
      'ko': '이 유형의 콘텐츠 표시가 현재 지원되지 않습니다',
      'pt': 'A exibição deste tipo de conteúdo não é atualmente suportada',
      'ru': 'Отображение этого типа контента в настоящее время не поддерживается',
      'th': 'การแสดงเนื้อหาประเภทนี้ไม่ได้รับการสนับสนุนในปัจจุบัน',
      'vi': 'Hiển thị loại nội dung này hiện không được hỗ trợ',
      'de': 'Die Anzeige dieses Inhalts wird derzeit nicht unterstützt',
      'es': 'La visualización de este tipo de contenido no está actualmente soportada'
    };
    window.bitable = bitable;
    (async () => {
      const lang = await bitable.bridge.getLanguage();
      bitable.bridge.getTheme().then((theme) => {
        updateTheme(theme.toLocaleLowerCase())
      });
      bitable.bridge.onThemeChange((e) => {
        updateTheme(e.data.theme.toLocaleLowerCase())
      })
      content.innerText = langMap[lang]
    })();
  </script>
</body>

</html>