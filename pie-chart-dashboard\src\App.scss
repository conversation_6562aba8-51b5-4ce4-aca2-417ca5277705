@import 'reset-css';

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, Helvetica Neue, Tahoma, PingFang SC, Microsoft Yahei, Arial, Hiragino Sans GB, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 日文环境字体
[lang="jp"] body {
  font-family: "ヒラギノ角ゴ Pro W3", "Hiragino Kaku Gothic Pro", "Yu Gothic UI", "游ゴシック体", "Noto Sans Japanese", "Microsoft Jhenghei UI", "Microsoft Yahei UI", "ＭＳ Ｐゴシック", Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
}

#root {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

// 主题适配
[theme-mode="dark"] {
  color-scheme: dark;
}

[theme-mode="light"] {
  color-scheme: light;
}
