import React from 'react';
import './style.scss';

interface ItemProps {
  label: React.ReactNode;
  children: React.ReactNode;
  className?: string;
}

export function Item({ label, children, className = '' }: ItemProps) {
  return (
    <div className={`config-item ${className}`}>
      <div className="config-item-label">{label}</div>
      <div className="config-item-content">{children}</div>
    </div>
  );
}
