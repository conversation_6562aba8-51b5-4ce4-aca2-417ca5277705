.main {
  display: flex;
  height: 100vh;
  width: 100vw;
  position: relative;
  overflow: hidden;

  &-config {
    .content {
      flex: 1;
      padding: 16px;
    }
  }

  .content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
  }
}

.chart-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 300px;

  &-config {
    height: calc(100vh - 32px);
  }

  .chart-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--semi-color-text-0);
    text-align: center;
    margin-bottom: 16px;
    padding: 0 16px;
  }

  &.chart-size-small {
    max-width: 400px;
    max-height: 300px;
  }

  &.chart-size-medium {
    max-width: 600px;
    max-height: 450px;
  }

  &.chart-size-large {
    max-width: 800px;
    max-height: 600px;
  }
}

.empty-state,
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;

  .empty-text,
  .loading-text {
    font-size: 16px;
    color: var(--semi-color-text-2);
  }
}

.config-panel {
  border-left: 1px solid var(--semi-color-border);
  flex: 0 0 340px;
  height: 100vh;
  padding: 20px;
  padding-bottom: 70px;
  position: relative;
  overflow-y: auto;
  background: var(--semi-color-bg-1);

  .form {
    height: calc(100% - 60px);
    overflow-y: auto;
  }

  .btn {
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;
    width: calc(100% - 40px);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .main-config {
    flex-direction: column;

    .config-panel {
      flex: 0 0 auto;
      height: 50vh;
      border-left: none;
      border-top: 1px solid var(--semi-color-border);
    }

    .content {
      height: 50vh;
    }
  }
}

// 全屏模式适配
[theme-mode="dark"] {
  .chart-container .chart-title {
    color: var(--semi-color-text-0);
  }

  .empty-state .empty-text,
  .loading-state .loading-text {
    color: var(--semi-color-text-2);
  }
}
