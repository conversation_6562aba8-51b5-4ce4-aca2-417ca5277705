// 颜色方案配置
export const getColorSchemes = (t: any) => ({
  default: {
    name: t('color.scheme.default'),
    colors: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc']
  },
  blue: {
    name: t('color.scheme.blue'),
    colors: ['#1890ff', '#40a9ff', '#69c0ff', '#91d5ff', '#bae7ff', '#e6f7ff', '#0050b3', '#003a8c', '#002766']
  },
  green: {
    name: t('color.scheme.green'),
    colors: ['#52c41a', '#73d13d', '#95de64', '#b7eb8f', '#d9f7be', '#f6ffed', '#389e0d', '#237804', '#135200']
  },
  orange: {
    name: t('color.scheme.orange'),
    colors: ['#fa8c16', '#ffa940', '#ffc069', '#ffd591', '#ffe7ba', '#fff7e6', '#d46b08', '#ad4e00', '#873800']
  },
  purple: {
    name: t('color.scheme.purple'),
    colors: ['#722ed1', '#9254de', '#b37feb', '#d3adf7', '#efdbff', '#f9f0ff', '#531dab', '#391085', '#22075e']
  }
});

// 处理图表数据
export const processChartData = (rawData: any, config: any) => {
  if (!rawData || !rawData.length) {
    return [];
  }

  // 假设数据格式为二维数组，第一行是表头，后续行是数据
  const [headers, ...rows] = rawData;
  
  if (!headers || !rows.length) {
    return [];
  }

  // 找到分组字段和数值字段的索引
  const groupIndex = headers.findIndex((header: any) => 
    header.fieldId === config.groupFieldId
  );
  const valueIndex = headers.findIndex((header: any) => 
    header.fieldId === config.valueFieldId
  );

  if (groupIndex === -1 || valueIndex === -1) {
    return [];
  }

  // 聚合数据
  const dataMap = new Map();
  
  rows.forEach((row: any[]) => {
    const groupValue = row[groupIndex]?.value || '未分类';
    const numValue = parseFloat(row[valueIndex]?.value) || 0;
    
    if (dataMap.has(groupValue)) {
      dataMap.set(groupValue, dataMap.get(groupValue) + numValue);
    } else {
      dataMap.set(groupValue, numValue);
    }
  });

  // 转换为图表数据格式
  return Array.from(dataMap.entries()).map(([name, value]) => ({
    name,
    value
  }));
};

// 生成图表配置
export const getChartOption = (data: any[], config: any, t: any) => {
  const colorSchemes = getColorSchemes(t);
  const currentScheme = colorSchemes[config.colorScheme] || colorSchemes.default;

  const option = {
    color: currentScheme.colors,
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      show: config.showLegend,
      orient: 'vertical',
      left: 'left',
      textStyle: {
        color: 'var(--semi-color-text-0)'
      }
    },
    series: [
      {
        name: config.title || t('title'),
        type: 'pie',
        radius: config.chartSize === 'small' ? '50%' : 
                config.chartSize === 'large' ? '70%' : '60%',
        center: config.showLegend ? ['60%', '50%'] : ['50%', '50%'],
        data: data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          show: config.showLabel,
          color: 'var(--semi-color-text-0)',
          formatter: '{b}: {c} ({d}%)'
        },
        labelLine: {
          show: config.showLabel
        }
      }
    ]
  };

  return option;
};
