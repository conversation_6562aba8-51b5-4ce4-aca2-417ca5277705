run = "npm run dev"
entrypoint = "src/App.jsx"

hidden = [".config", "tsconfig.json", "tsconfig.node.json", "vite.config.js", ".gitignore"]

[nix]
channel = "stable-22_11"

[env]
PATH = "/home/<USER>/$REPL_SLUG/.config/npm/node_global/bin:/home/<USER>/$REPL_SLUG/node_modules/.bin"
npm_config_prefix = "/home/<USER>/$REPL_SLUG/.config/npm/node_global"

[gitHubImport]
requiredFiles = [".replit", "replit.nix", ".config"]

[packager]
language = "nodejs"
  [packager.features]
  packageSearch = true
  guessImports = true
  enabledForHosting = false

[languages]
  [languages.javascript]
  pattern = "**/{*.js,*.jsx,*.ts,*.tsx}"
    [languages.javascript.languageServer]
    start = "typescript-language-server --stdio"

[deployment]
build = ["sh", "-c", "npm run build"]
run = ["sh", "-c", "npm run preview"]